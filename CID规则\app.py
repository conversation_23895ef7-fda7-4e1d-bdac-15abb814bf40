# app.py
import os
import re
import sqlite3
import configparser
import time
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, abort
import logging
from werkzeug.utils import secure_filename

# 导入 scraper 模块和自定义错误
import avbase_scraper
from avbase_scraper import ScrapeError

# --- App 初始化 ---
app = Flask(__name__)
app.secret_key = os.urandom(24) 
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024 # 16MB max upload size
app.config['STATIC_FOLDER'] = 'static'

# 配置日志记录
logging.basicConfig(level=logging.INFO)

# --- 配置 ---
config = configparser.ConfigParser()
config.read('config.ini')
DB_PATH = config.get('Database', 'Path', fallback='number_cid.db')

# --- 数据库连接辅助函数 ---
def get_db_conn():
    """获取数据库连接，并启用外键约束"""
    conn = sqlite3.connect(DB_PATH)
    conn.execute("PRAGMA foreign_keys = ON;")
    conn.row_factory = sqlite3.Row
    return conn

# --- 分页范围计算辅助函数 ---
def get_pagination_range(current_page, total_pages, window=2):
    """计算智能分页的页码范围"""
    if total_pages <= (window * 2 + 3): return range(1, total_pages + 1)
    pages = [1]
    if current_page > window + 2: pages.append(None) # '...'
    start = max(2, current_page - window)
    end = min(total_pages - 1, current_page + window)
    for i in range(start, end + 1):
        if i not in pages: pages.append(i)
    if current_page < total_pages - window - 1: pages.append(None) # '...'
    if total_pages not in pages: pages.append(total_pages)
    return pages

# --- 辅助函数：检查范围 ---
def check_range(number, range_str):
    if not range_str:
        return False
    
    number = int(number)
    parts = [p.strip() for p in range_str.split(',')]
    
    for part in parts:
        if '>=' in part:
            if number >= int(part.replace('>=', '')): return True
        elif '<=' in part:
            if number <= int(part.replace('<=', '')): return True
        elif '-' in part:
            start, end = map(int, part.split('-'))
            if start <= number <= end: return True
        else:
            if number == int(part): return True
            
    return False

# --- 页面路由 ---
@app.route('/')
def index():
    """首页重定向到系列规则页面"""
    return redirect(url_for('view_series_rules'))

@app.route('/series_rules')
def view_series_rules():
    # ... (此函数保持不变)
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status_filter', 'all')
    per_page = 15
    offset = (page - 1) * per_page
    
    conn = get_db_conn()
    try:
        params = []
        base_query_from = "FROM cid_conversion_rules m"
        where_clauses = []
        
        if search:
            where_clauses.append("m.series_name LIKE ?")
            params.append(f'%{search}%')
        
        if status_filter != 'all':
            where_clauses.append("m.status = ?")
            params.append(status_filter)
        
        where_sql = ""
        if where_clauses:
            where_sql = " WHERE " + " AND ".join(where_clauses)
            
        total_query = "SELECT COUNT(m.id) " + base_query_from + where_sql
        total = conn.execute(total_query, tuple(params)).fetchone()[0]
        
        data_query = f"""
            SELECT m.*, COUNT(v.id) as variant_count 
            {base_query_from}
            LEFT JOIN series_name_variants v ON m.id = v.main_rule_id
            {where_sql}
            GROUP BY m.id 
            ORDER BY m.id
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])
        rules_rows = conn.execute(data_query, tuple(params)).fetchall()
        
        rules = [dict(row) for row in rules_rows]
    finally:
        conn.close()
    
    total_pages = (total + per_page - 1) // per_page
    pagination_range = get_pagination_range(page, total_pages)
    
    return render_template(
        'series_rules.html', 
        rules=rules, page=page, total_pages=total_pages, 
        search=search, pagination_range=pagination_range,
        total=total, offset=offset, status_filter=status_filter
    )


@app.route('/special_rules')
def view_special_rules():
    # ... (此函数保持不变)
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    per_page = 15
    offset = (page - 1) * per_page
    conn = get_db_conn()
    try:
        if search:
            rules_rows = conn.execute("SELECT * FROM direct_cid_map WHERE number LIKE ? OR cid LIKE ? ORDER BY id DESC LIMIT ? OFFSET ?", (f'%{search}%', f'%{search}%', per_page, offset)).fetchall()
            total = conn.execute("SELECT COUNT(*) FROM direct_cid_map WHERE number LIKE ? OR cid LIKE ?", (f'%{search}%', f'%{search}%')).fetchone()[0]
        else:
            rules_rows = conn.execute("SELECT * FROM direct_cid_map ORDER BY id DESC LIMIT ? OFFSET ?", (per_page, offset)).fetchall()
            total = conn.execute("SELECT COUNT(*) FROM direct_cid_map").fetchone()[0]
        rules = [dict(row) for row in rules_rows]
    finally:
        conn.close()
    total_pages = (total + per_page - 1) // per_page
    pagination_range = get_pagination_range(page, total_pages)
    return render_template('special_rules.html', rules=rules, page=page, total_pages=total_pages, search=search, pagination_range=pagination_range)


# --- 新增页面路由 ---
@app.route('/generate_cid')
def generate_cid():
    """渲染 CID 生成器页面"""
    return render_template('generate_cid.html')


# --- API 路由 ---

# --- 新增API端点 ---
@app.route('/api/generate_cid', methods=['POST'])
def api_generate_cid():
    bangou = request.get_json().get('bangou', '').upper()
    if not bangou:
        return jsonify({"error": "请输入番号。"}), 400

    match = avbase_scraper.BANGOU_REGEX.match(bangou)
    if not match:
        return jsonify({"error": "无法从输入中解析出番号系列和数字。"}), 400
    
    series_name = match.group(1)
    number_part = match.group(2)

    conn = get_db_conn()
    
    # 1. 检查特例规则
    special_rule = conn.execute("SELECT * FROM direct_cid_map WHERE number = ?", (bangou,)).fetchone()
    if special_rule:
        cid = special_rule['cid']
        code = cid.replace('_', '')
        results = [{
            "cid": cid,
            "source": "特例规则",
            "description": f"直接匹配",
            "wallpaper_url": f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}pl.jpg',
            "cover_url": f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}ps.jpg'
        }]
        conn.close()
        return jsonify(results)

    # 2. 检查主规则和变体规则
    main_rule = conn.execute("SELECT * FROM cid_conversion_rules WHERE series_name = ?", (series_name,)).fetchone()
    if not main_rule:
        conn.close()
        return jsonify({"error": f"未找到系列 '{series_name}' 的主规则。"}), 404

    variants = conn.execute("SELECT * FROM series_name_variants WHERE main_rule_id = ?", (main_rule['id'],)).fetchall()
    conn.close()

    applicable_rules = []
    
    # 检查所有范围变体
    range_variant_applied = False
    for v in variants:
        if v['is_range_variant'] and check_range(number_part, v['serial_number_range']):
            applicable_rules.append({"rule": dict(v), "source": "变体规则", "description": f"匹配范围 '{v['serial_number_range']}'"})
            range_variant_applied = True
            break # 假设只有一个范围变体能匹配

    # 如果没有范围变体匹配，则使用主规则
    if not range_variant_applied:
        applicable_rules.append({"rule": dict(main_rule), "source": "主规则", "description": "默认规则"})

    # 添加所有片商变体
    for v in variants:
        if not v['is_range_variant']:
            applicable_rules.append({"rule": dict(v), "source": "变体规则", "description": f"片商 '{v['studio']}'"})

    # 生成最终结果
    results = []
    for item in applicable_rules:
        rule = item['rule']
        padded_number = number_part.zfill(rule['number_padding'])
        cid = f"{rule['cid_prefix'] or ''}{rule['series_name_in_cid'] or ''}{padded_number}{rule['suffix'] or ''}"
        code = cid.replace('_', '')
        
        results.append({
            "cid": cid,
            "source": item['source'],
            "description": item['description'],
            "wallpaper_url": f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}pl.jpg',
            "cover_url": f'https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}ps.jpg'
        })
        
    return jsonify(results)


# ... (其他API保持不变)
@app.route('/api/avbase_lookup', methods=['POST'])
def avbase_lookup():
    # ...
    search_term = request.get_json().get('bangou')
    if not search_term:
        return jsonify({"success": False, "message": "请求中未提供番号。"}), 400

    conn = None
    try:
        scraped_data = avbase_scraper.scrape_cid_and_bangou(search_term)
        full_bangou = scraped_data['bangou']
        correct_cid = scraped_data['cid']

        new_rule = avbase_scraper.derive_rule_from_cid(full_bangou, correct_cid)
        
        conn = get_db_conn()
        is_conflict, main_rule_id = check_rule_conflict(conn, new_rule)
        
        details = {
            "bangou": full_bangou.upper(), 
            "cid": correct_cid, 
            "new_rule": new_rule,
            "main_rule_id": main_rule_id
        }

        if not main_rule_id:
            return jsonify({"success": True, "message": f"发现新系列 {new_rule['series_name']} 的规则，已填充表单。", "details": details})

        if not is_conflict:
            conn.execute("UPDATE cid_conversion_rules SET status='已确认' WHERE id=?", (main_rule_id,))
            conn.commit()
            return jsonify({"success": True, "message": f"规则与现有规则一致。系列 {new_rule['series_name']} 的状态已更新为“已确认”。", "details": details})
        
        conflict_message = (f"为系列 '{new_rule['series_name']}' 发现了一条新的、不匹配的规则。\n\n"
                            f"【推导出的新规则】: \n"
                            f"前缀='{new_rule['prefix']}', CID系列名='{new_rule['series_name_in_cid']}', 补位={new_rule['padding']}, 后缀='{new_rule['suffix']}'\n\n"
                            "请选择如何处理这条新规则：")
        return jsonify({
            "success": False, 
            "conflict_type": "new_variant",
            "message": conflict_message, 
            "details": details
        }), 409

    except (ScrapeError, ValueError) as e:
        return jsonify({"success": False, "message": str(e)}), 500
    except Exception as e:
        app.logger.error(f"AVBase查找时发生内部错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"服务器内部错误: {e}"}), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/series_rules/add', methods=['POST'])
def add_series_rule():
    # ...
    data = request.get_json()
    new_rule = {
        "series_name": data.get('series_name').upper(),
        "prefix": data.get('cid_prefix', ''),
        "series_name_in_cid": data.get('series_name_in_cid', '').lower(),
        "padding": int(data.get('number_padding', 0)),
        "suffix": data.get('suffix', '')
    }
    
    conn = get_db_conn()
    try:
        is_conflict, main_rule_id = check_rule_conflict(conn, new_rule)

        if not main_rule_id:
            conn.execute("INSERT INTO cid_conversion_rules (series_name, cid_prefix, series_name_in_cid, number_padding, suffix, status) VALUES (?, ?, ?, ?, ?, ?)", 
                         (new_rule['series_name'], new_rule['prefix'], new_rule['series_name_in_cid'], new_rule['padding'], new_rule['suffix'], data.get('status', '已确认')))
            conn.commit()
            return jsonify({"success": True, "message": "主规则添加成功！"})

        if not is_conflict:
            return jsonify({"success": True, "message": "规则已存在，无需操作。"})

        conflict_message = (f"系列 '{new_rule['series_name']}' 已存在主规则，且您输入的新规则不匹配任何现有规则。\n\n"
                            "请选择如何处理这条新规则：")
        return jsonify({
            "success": False, 
            "conflict_type": "new_variant",
            "message": conflict_message, 
            "details": {
                "bangou": None,
                "cid": None,
                "new_rule": new_rule,
                "main_rule_id": main_rule_id
            }
        }), 409
    finally:
        conn.close()

@app.route('/api/series_rules/update/<int:rule_id>', methods=['POST'])
def update_series_rule(rule_id):
    # ...
    data = request.get_json()
    conn = get_db_conn()
    try:
        conn.execute("UPDATE cid_conversion_rules SET series_name=?, cid_prefix=?, series_name_in_cid=?, number_padding=?, suffix=?, status=? WHERE id=?",
                     (data['series_name'], data['cid_prefix'], data['series_name_in_cid'], data['number_padding'], data['suffix'], data['status'], rule_id))
        conn.commit()
        return jsonify({"success": True, "message": "主规则更新成功！"})
    except sqlite3.IntegrityError:
        return jsonify({"success": False, "message": f"主规则系列 '{data['series_name']}' 与其他规则冲突！"}), 409
    finally:
        conn.close()

@app.route('/api/series_rules/delete/<int:rule_id>', methods=['DELETE'])
def delete_series_rule(rule_id):
    # ...
    conn = get_db_conn()
    try:
        conn.execute("DELETE FROM cid_conversion_rules WHERE id=?", (rule_id,))
        conn.commit()
        return jsonify({"success": True, "message": "主规则及其所有变体规则已删除。"})
    finally:
        conn.close()

@app.route('/api/variants/<int:main_rule_id>', methods=['GET'])
def get_variants(main_rule_id):
    # ...
    conn = get_db_conn()
    try:
        main_rule = conn.execute("SELECT * FROM cid_conversion_rules WHERE id = ?", (main_rule_id,)).fetchone()
        if not main_rule:
            abort(404, description="未找到指定的主规则。")
        
        variants = conn.execute("SELECT * FROM series_name_variants WHERE main_rule_id = ? ORDER BY id", (main_rule_id,)).fetchall()
        
        return jsonify({
            "main_rule": dict(main_rule),
            "variants": [dict(v) for v in variants]
        })
    finally:
        conn.close()

@app.route('/api/variants/add', methods=['POST'])
def add_variant_rule():
    # ...
    data = request.get_json()
    is_range_variant = data.get('is_range_variant', True)
    
    if is_range_variant and not data.get('serial_number_range'):
        return jsonify({"success": False, "message": "范围变体必须填写“适用番号范围”。"}), 400
    if not is_range_variant and not data.get('studio'):
        return jsonify({"success": False, "message": "片商变体必须填写“片商”。"}), 400

    conn = get_db_conn()
    try:
        conn.execute("""
            INSERT INTO series_name_variants 
            (main_rule_id, is_range_variant, serial_number_range, studio, cid_prefix, series_name_in_cid, number_padding, suffix, description) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data['main_rule_id'], is_range_variant, data.get('serial_number_range'), data.get('studio'),
            data.get('cid_prefix', ''), data['series_name_in_cid'], 
            data['number_padding'], data.get('suffix', ''), data.get('description', '')
        ))
        conn.commit()
        return jsonify({"success": True, "message": "变体规则添加成功。"})
    except sqlite3.Error as e:
        app.logger.error(f"添加变体规则时数据库出错: {e}")
        return jsonify({"success": False, "message": f"数据库错误: {e}"}), 500
    finally:
        conn.close()

@app.route('/api/variants/update/<int:variant_id>', methods=['POST'])
def update_variant_rule(variant_id):
    # ...
    data = request.get_json()
    is_range_variant = data.get('is_range_variant', True)

    if is_range_variant and not data.get('serial_number_range'):
        return jsonify({"success": False, "message": "范围变体必须填写“适用番号范围”。"}), 400
    if not is_range_variant and not data.get('studio'):
        return jsonify({"success": False, "message": "片商变体必须填写“片商”。"}), 400

    conn = get_db_conn()
    try:
        conn.execute("""
            UPDATE series_name_variants SET
            is_range_variant=?, serial_number_range=?, studio=?, cid_prefix=?, series_name_in_cid=?, 
            number_padding=?, suffix=?, description=?
            WHERE id=?
        """, (
            is_range_variant, data.get('serial_number_range'), data.get('studio'),
            data.get('cid_prefix'), data.get('series_name_in_cid'),
            data.get('number_padding'), data.get('suffix'), data.get('description'), variant_id
        ))
        conn.commit()
        return jsonify({"success": True, "message": "变体规则更新成功。"})
    finally:
        conn.close()

@app.route('/api/variants/delete/<int:variant_id>', methods=['DELETE'])
def delete_variant_rule(variant_id):
    # ...
    conn = get_db_conn()
    try:
        conn.execute("DELETE FROM series_name_variants WHERE id=?", (variant_id,))
        conn.commit()
        return jsonify({"success": True, "message": "变体规则已删除。"})
    finally:
        conn.close()

@app.route('/api/special_rules/add', methods=['POST'])
def add_special_rule():
    # ...
    data = request.get_json()
    conn = get_db_conn()
    try:
        conn.execute("INSERT INTO direct_cid_map (number, cid) VALUES (?, ?)", (data['number'].upper(), data['cid']))
        conn.commit()
        return jsonify({"success": True, "message": "特例规则添加成功！"})
    except sqlite3.IntegrityError:
        return jsonify({"success": False, "message": f"特例番号 '{data['number']}' 已存在！"}), 409
    finally:
        conn.close()

@app.route('/api/special_rules/delete/<int:rule_id>', methods=['DELETE'])
def delete_special_rule(rule_id):
    # ...
    conn = get_db_conn()
    try:
        conn.execute("DELETE FROM direct_cid_map WHERE id=?", (rule_id,))
        conn.commit()
        return jsonify({"success": True, "message": "特例规则已删除。"})
    finally:
        conn.close()

@app.route('/api/parse_rule', methods=['POST'])
def parse_rule_from_string():
    # ...
    data = request.get_json()
    parse_string = data.get('text', '')
    parts = parse_string.split(',')
    if len(parts) != 2:
        return jsonify({"success": False, "message": "格式错误，应为 '番号,cid'"})
    
    bangou, cid = parts[0].strip(), parts[1].strip()
    try:
        rule = avbase_scraper.derive_rule_from_cid(bangou, cid)
        return jsonify({"success": True, "rule": rule})
    except ValueError as e:
        return jsonify({"success": False, "message": str(e)})

# --- 应用启动 ---
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=10086, debug=True)
