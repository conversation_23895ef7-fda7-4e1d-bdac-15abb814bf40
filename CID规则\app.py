# app.py
import os
import re
import sqlite3
import configparser
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask, render_template, request, jsonify, redirect, url_for, abort

# 导入 scraper 模块和自定义错误
import avbase_scraper
from avbase_scraper import ScrapeError

# --- App 初始化 ---
app = Flask(__name__)
app.secret_key = os.urandom(24) 
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024 # 16MB max upload size
app.config['STATIC_FOLDER'] = 'static'

# --- 配置 ---
config = configparser.ConfigParser()
config.read('config.ini')
DB_PATH = config.get('Database', 'Path', fallback='number_cid.db')
API_SECRET_KEY = config.get('API', 'SecretKey', fallback=None)
# --- MODIFICATION START: Read LogLevel from config ---
LOG_LEVEL_STR = config.get('Logging', 'LogLevel', fallback='INFO').upper()
LOG_LEVEL = getattr(logging, LOG_LEVEL_STR, logging.INFO)
# --- MODIFICATION END ---
BANGOU_REGEX = re.compile(r'([a-zA-Z\-_]+?)-?(\d+)')


# --- 日志配置 ---
if not os.path.exists('logs'):
    os.mkdir('logs')
file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240000, backupCount=5)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
# --- MODIFICATION: Use the configured log level ---
file_handler.setLevel(LOG_LEVEL)
app.logger.addHandler(file_handler)
app.logger.setLevel(LOG_LEVEL) 
app.logger.info(f'Application startup with log level {LOG_LEVEL_STR}')


# --- 数据库连接辅助函数 ---
def get_db_conn():
    """获取数据库连接，并启用外键约束"""
    conn = sqlite3.connect(DB_PATH)
    conn.execute("PRAGMA foreign_keys = ON;")
    conn.row_factory = sqlite3.Row
    return conn

# --- 分页范围计算辅助函数 ---
def get_pagination_range(current_page, total_pages, window=2):
    """计算智能分页的页码范围"""
    if total_pages <= (window * 2 + 3): return range(1, total_pages + 1)
    pages = [1]
    if current_page > window + 2: pages.append(None) # '...'
    start = max(2, current_page - window)
    end = min(total_pages - 1, current_page + window)
    for i in range(start, end + 1):
        if i not in pages: pages.append(i)
    if current_page < total_pages - window - 1: pages.append(None) # '...'
    if total_pages not in pages: pages.append(total_pages)
    return pages

# --- 页面路由 ---
@app.route('/')
def index():
    """首页重定向到系列规则页面"""
    return redirect(url_for('view_series_rules'))

@app.route('/series_rules')
def view_series_rules():
    """显示主规则列表，并包含变体规则数量"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status_filter', 'all')
    padding_filter = request.args.get('padding_filter', '')
    per_page = 15
    offset = (page - 1) * per_page
    
    conn = get_db_conn()
    try:
        params = []
        base_query_from = "FROM cid_conversion_rules m"
        where_clauses = []
        
        if search:
            where_clauses.append("m.series_name LIKE ?")
            params.append(f'%{search}%')
        
        if status_filter != 'all':
            where_clauses.append("m.status = ?")
            params.append(status_filter)
        
        if padding_filter:
            where_clauses.append("m.number_padding = ?")
            params.append(padding_filter)

        where_sql = ""
        if where_clauses:
            where_sql = " WHERE " + " AND ".join(where_clauses)
            
        total_query = "SELECT COUNT(m.id) " + base_query_from + where_sql
        total = conn.execute(total_query, tuple(params)).fetchone()[0]
        
        data_query = f"""
            SELECT m.*, COUNT(v.id) as variant_count 
            {base_query_from}
            LEFT JOIN series_name_variants v ON m.id = v.main_rule_id
            {where_sql}
            GROUP BY m.id 
            ORDER BY m.id ASC
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])
        rules_rows = conn.execute(data_query, tuple(params)).fetchall()
        
        rules = [dict(row) for row in rules_rows]
    finally:
        conn.close()
    
    total_pages = (total + per_page - 1) // per_page
    pagination_range = get_pagination_range(page, total_pages)
    
    return render_template(
        'series_rules.html', 
        rules=rules, page=page, total_pages=total_pages, 
        search=search, pagination_range=pagination_range,
        total=total, offset=offset, status_filter=status_filter,
        padding_filter=padding_filter
    )

@app.route('/cid_generator')
def cid_generator():
    """渲染CID生成器页面"""
    return render_template('cid_generator.html')

@app.route('/logs')
def view_logs():
    """渲染日志查看器页面"""
    return render_template('logs.html')

# --- API 路由 ---

def check_rule_conflict(conn, new_rule):
    main_rule = conn.execute("SELECT id FROM cid_conversion_rules WHERE series_name = ?", (new_rule['series_name'],)).fetchone()
    if not main_rule: return (False, None)
    main_rule_id = main_rule['id']
    query_params = (main_rule_id, main_rule_id, new_rule.get('prefix', ''), new_rule.get('series_name_in_cid', ''), new_rule.get('padding', 0), new_rule.get('suffix', ''))
    match_query = """
        SELECT 1 FROM (
            SELECT cid_prefix, series_name_in_cid, number_padding, suffix FROM cid_conversion_rules WHERE id = ?
            UNION ALL
            SELECT cid_prefix, series_name_in_cid, number_padding, suffix FROM series_name_variants WHERE main_rule_id = ?
        ) AS all_rules
        WHERE COALESCE(cid_prefix, '') = ? AND COALESCE(series_name_in_cid, '') = ? AND number_padding = ? AND COALESCE(suffix, '') = ?
        LIMIT 1
    """
    exact_match = conn.execute(match_query, query_params).fetchone()
    return (False, main_rule_id) if exact_match else (True, main_rule_id)

@app.route('/api/avbase_lookup', methods=['POST'])
def avbase_lookup():
    bangou = request.get_json().get('bangou')
    if not bangou: return jsonify({"success": False, "message": "请求中未提供番号。"}), 400
    conn = None
    try:
        correct_cid = avbase_scraper.scrape_cid(bangou)
        new_rule = avbase_scraper.derive_rule_from_cid(bangou, correct_cid)
        conn = get_db_conn()
        is_conflict, main_rule_id = check_rule_conflict(conn, new_rule)
        details = {"bangou": bangou.upper(), "cid": correct_cid, "new_rule": new_rule, "main_rule_id": main_rule_id}
        if not main_rule_id: return jsonify({"success": True, "message": f"发现新系列 {new_rule['series_name']} 的规则，已填充表单。", "details": details})
        if not is_conflict:
            conn.execute("UPDATE cid_conversion_rules SET status='已确认' WHERE id=?", (main_rule_id,))
            conn.commit()
            return jsonify({"success": True, "message": f"规则与现有规则一致。系列 {new_rule['series_name']} 的状态已更新为“已确认”。", "details": details})
        conflict_message = (f"为系列 '{new_rule['series_name']}' 发现了一条新的、不匹配的规则。\n\n"
                            f"【推导出的新规则】: \n"
                            f"前缀='{new_rule['prefix']}', CID系列名='{new_rule['series_name_in_cid']}', 补位={new_rule['padding']}, 后缀='{new_rule['suffix']}'\n\n"
                            "请选择如何处理这条新规则：")
        return jsonify({"success": False, "conflict_type": "new_variant", "message": conflict_message, "details": details}), 409
    except (ScrapeError, ValueError) as e: return jsonify({"success": False, "message": str(e)}), 500
    except Exception as e:
        app.logger.error(f"AVBase查找时发生内部错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"服务器内部错误: {e}"}), 500
    finally:
        if conn: conn.close()

def evaluate_range(num, range_str):
    if not range_str: return False
    try:
        num = int(num)
        parts = re.split(r'(>=|<=|>|<|=)', range_str)
        parts = [p.strip() for p in parts if p.strip()]
        if len(parts) == 2:
            op, val = parts; val = int(val)
            if op == '>=': return num >= val
            if op == '<=': return num <= val
            if op == '>': return num > val
            if op == '<': return num < val
            if op == '=': return num == val
        elif '-' in range_str:
            start, end = map(int, range_str.split('-'))
            return start <= num <= end
        else:
            return num == int(range_str)
    except (ValueError, TypeError): return False

def get_applicable_rules(conn, series_name, num_part):
    """根据系列名和数字部分，从数据库获取所有适用的规则"""
    main_rule = conn.execute("SELECT * FROM cid_conversion_rules WHERE series_name = ?", (series_name,)).fetchone()
    if not main_rule:
        return None, f"未找到系列 '{series_name}' 的主规则。"
    
    variants = conn.execute("SELECT * FROM series_name_variants WHERE main_rule_id = ? ORDER BY id", (main_rule['id'],)).fetchall()
    
    applicable_rules = []
    applied_range_rule = False

    # 1. 检查范围变体规则
    for v in variants:
        if v['is_range_variant'] and evaluate_range(num_part, v['serial_number_range']):
            rule_info = f"变体规则 (ID: {v['id']}, 范围: {v['serial_number_range']})"
            applicable_rules.append({"rule": dict(v), "num_part": num_part, "rule_info": rule_info})
            applied_range_rule = True
            break

    # 2. 如果没有应用范围规则，则检查片商规则和主规则
    if not applied_range_rule:
        for v in variants:
            if not v['is_range_variant']:
                rule_info = f"变体规则 (ID: {v['id']}, 片商: {v['studio']})"
                applicable_rules.append({"rule": dict(v), "num_part": num_part, "rule_info": rule_info})
        
        applicable_rules.append({"rule": dict(main_rule), "num_part": num_part, "rule_info": "主规则"})
        
    return applicable_rules, None

@app.route('/api/generate_cid', methods=['POST'])
def generate_cid():
    bangou = request.get_json().get('bangou', '').upper()
    if not bangou: return jsonify({"success": False, "message": "请输入番号。"}), 400
    match = BANGOU_REGEX.match(bangou)
    if not match: return jsonify({"success": False, "message": "无法从番号中解析出系列和数字部分。"}), 400
    
    series_name, num_part = match.groups()
    conn = get_db_conn()
    try:
        applicable_rules, error_msg = get_applicable_rules(conn, series_name, num_part)
        if error_msg: return jsonify({"success": False, "message": error_msg}), 404

        final_results = []
        for res in applicable_rules:
            rule = res['rule']
            padded_num = res['num_part'].zfill(rule['number_padding'])
            cid = f"{rule.get('cid_prefix', '') or ''}{rule.get('series_name_in_cid', '')}{padded_num}{rule.get('suffix', '') or ''}"
            code_num_part_match = re.search(r'(\d+)', cid)
            dmm_code = cid
            if code_num_part_match:
                num_in_cid = code_num_part_match.group(1)
                if cid.endswith(num_in_cid + (rule.get('suffix', '') or '')):
                     dmm_code = cid.replace(num_in_cid, num_in_cid.zfill(5))
            final_results.append({
                "cid": cid, "rule_info": res['rule_info'],
                "dmm_digital_url": f"https://www.dmm.co.jp/digital/videoa/-/detail/=/cid={cid}/",
                "dmm_mono_url": f"https://www.dmm.co.jp/mono/dvd/-/detail/=/cid={cid}/",
                "dmm_wallpaper_url": f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{dmm_code}/{dmm_code}pl.jpg",
                "dmm_cover_url": f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{dmm_code}/{dmm_code}ps.jpg"
            })
        return jsonify({"success": True, "results": final_results})
    finally:
        conn.close()

@app.route('/api/get_cid', methods=['GET'])
def get_cid_external():
    if API_SECRET_KEY:
        provided_key = request.headers.get('X-API-KEY')
        if not provided_key or provided_key != API_SECRET_KEY:
            return jsonify({"success": False, "message": "授权失败: 无效的 API Key。"}), 403

    bangou = request.args.get('bangou', '').upper()
    if not bangou:
        return jsonify({"success": False, "message": "请求参数 'bangou' 不能为空。"}), 400

    match = BANGOU_REGEX.match(bangou)
    if not match:
        return jsonify({"success": False, "message": "无法从番号中解析出系列和数字部分。"}), 400
        
    series_name, num_part = match.groups()
    conn = get_db_conn()
    try:
        applicable_rules, error_msg = get_applicable_rules(conn, series_name, num_part)
        if error_msg:
            return jsonify({"success": False, "message": error_msg}), 404

        results = []
        for res in applicable_rules:
            rule = res['rule']
            padded_num = res['num_part'].zfill(rule['number_padding'])
            cid = f"{rule.get('cid_prefix', '') or ''}{rule.get('series_name_in_cid', '')}{padded_num}{rule.get('suffix', '') or ''}"
            results.append({ "cid": cid, "rule_info": res['rule_info'] })
            
        return jsonify({ "success": True, "bangou": bangou, "results": results })
    finally:
        conn.close()

@app.route('/api/logs', methods=['GET'])
def get_logs():
    """读取并返回应用日志"""
    level = request.args.get('level', 'ALL').upper()
    log_file_path = 'logs/app.log'
    
    if not os.path.exists(log_file_path):
        return jsonify({"success": False, "message": "日志文件不存在。"}), 404
        
    logs = []
    log_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) (\w+): (.*)')

    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                match = log_pattern.match(line)
                if match:
                    timestamp, log_level, message = match.groups()
                    if level == 'ALL' or log_level == level:
                        logs.append({
                            "timestamp": timestamp,
                            "level": log_level,
                            "message": message.strip()
                        })
        # Return logs in reverse chronological order
        return jsonify({"success": True, "logs": logs[::-1]})
    except Exception as e:
        app.logger.error(f"读取日志文件时出错: {e}")
        return jsonify({"success": False, "message": f"读取日志文件时出错: {e}"}), 500


# --- 主规则 CRUD API ---
@app.route('/api/series_rules/add', methods=['POST'])
def add_series_rule():
    data = request.get_json()
    new_rule = {"series_name": data.get('series_name').upper(), "prefix": data.get('cid_prefix', ''), "series_name_in_cid": data.get('series_name_in_cid', '').lower(), "padding": int(data.get('number_padding', 0)), "suffix": data.get('suffix', '')}
    conn = get_db_conn()
    try:
        is_conflict, main_rule_id = check_rule_conflict(conn, new_rule)
        if not main_rule_id:
            conn.execute("INSERT INTO cid_conversion_rules (series_name, cid_prefix, series_name_in_cid, number_padding, suffix, status) VALUES (?, ?, ?, ?, ?, ?)", 
                         (new_rule['series_name'], new_rule['prefix'], new_rule['series_name_in_cid'], new_rule['padding'], new_rule['suffix'], data.get('status', '已确认')))
            conn.commit()
            return jsonify({"success": True, "message": "主规则添加成功！"})
        if not is_conflict: return jsonify({"success": True, "message": "规则已存在，无需操作。"})
        conflict_message = (f"系列 '{new_rule['series_name']}' 已存在主规则，且您输入的新规则不匹配任何现有规则。\n\n" "请选择如何处理这条新规则：")
        return jsonify({"success": False, "conflict_type": "new_variant", "message": conflict_message, "details": {"bangou": None, "cid": None, "new_rule": new_rule, "main_rule_id": main_rule_id}}), 409
    finally: conn.close()

@app.route('/api/series_rules/update/<int:rule_id>', methods=['POST'])
def update_series_rule(rule_id):
    data = request.get_json()
    conn = get_db_conn()
    try:
        conn.execute("UPDATE cid_conversion_rules SET series_name=?, cid_prefix=?, series_name_in_cid=?, number_padding=?, suffix=?, status=? WHERE id=?",
                     (data['series_name'], data['cid_prefix'], data['series_name_in_cid'], data['number_padding'], data['suffix'], data['status'], rule_id))
        conn.commit()
        return jsonify({"success": True, "message": "主规则更新成功！"})
    except sqlite3.IntegrityError: return jsonify({"success": False, "message": f"主规则系列 '{data['series_name']}' 与其他规则冲突！"}), 409
    finally: conn.close()

@app.route('/api/series_rules/delete/<int:rule_id>', methods=['DELETE'])
def delete_series_rule(rule_id):
    conn = get_db_conn()
    try:
        conn.execute("DELETE FROM cid_conversion_rules WHERE id=?", (rule_id,))
        conn.commit()
        return jsonify({"success": True, "message": "主规则及其所有变体规则已删除。"})
    finally: conn.close()

# --- 变体规则 CRUD API ---
@app.route('/api/variants/<int:main_rule_id>', methods=['GET'])
def get_variants(main_rule_id):
    conn = get_db_conn()
    try:
        main_rule = conn.execute("SELECT * FROM cid_conversion_rules WHERE id = ?", (main_rule_id,)).fetchone()
        if not main_rule: abort(404, description="未找到指定的主规则。")
        variants = conn.execute("SELECT * FROM series_name_variants WHERE main_rule_id = ? ORDER BY id", (main_rule_id,)).fetchall()
        return jsonify({"main_rule": dict(main_rule), "variants": [dict(v) for v in variants]})
    finally: conn.close()

@app.route('/api/variants/add', methods=['POST'])
def add_variant_rule():
    data = request.get_json()
    is_range_variant = data.get('is_range_variant', True)
    if is_range_variant and not data.get('serial_number_range'): return jsonify({"success": False, "message": "范围变体必须填写“适用番号范围”。"}), 400
    if not is_range_variant and not data.get('studio'): return jsonify({"success": False, "message": "片商变体必须填写“片商”。"}), 400
    conn = get_db_conn()
    try:
        conn.execute("""
            INSERT INTO series_name_variants (main_rule_id, is_range_variant, serial_number_range, studio, cid_prefix, series_name_in_cid, number_padding, suffix, description) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""", 
            (data['main_rule_id'], is_range_variant, data.get('serial_number_range'), data.get('studio'), data.get('cid_prefix', ''), data['series_name_in_cid'], data['number_padding'], data.get('suffix', ''), data.get('description', '')))
        conn.commit()
        return jsonify({"success": True, "message": "变体规则添加成功。"})
    except sqlite3.Error as e:
        app.logger.error(f"添加变体规则时数据库出错: {e}")
        return jsonify({"success": False, "message": f"数据库错误: {e}"}), 500
    finally: conn.close()

@app.route('/api/variants/update/<int:variant_id>', methods=['POST'])
def update_variant_rule(variant_id):
    data = request.get_json()
    is_range_variant = data.get('is_range_variant', True)
    if is_range_variant and not data.get('serial_number_range'): return jsonify({"success": False, "message": "范围变体必须填写“适用番号范围”。"}), 400
    if not is_range_variant and not data.get('studio'): return jsonify({"success": False, "message": "片商变体必须填写“片商”。"}), 400
    conn = get_db_conn()
    try:
        conn.execute("UPDATE series_name_variants SET is_range_variant=?, serial_number_range=?, studio=?, cid_prefix=?, series_name_in_cid=?, number_padding=?, suffix=?, description=? WHERE id=?",
                     (is_range_variant, data.get('serial_number_range'), data.get('studio'), data.get('cid_prefix'), data.get('series_name_in_cid'), data.get('number_padding'), data.get('suffix'), data.get('description'), variant_id))
        conn.commit()
        return jsonify({"success": True, "message": "变体规则更新成功。"})
    finally: conn.close()

@app.route('/api/variants/delete/<int:variant_id>', methods=['DELETE'])
def delete_variant_rule(variant_id):
    conn = get_db_conn()
    try:
        conn.execute("DELETE FROM series_name_variants WHERE id=?", (variant_id,))
        conn.commit()
        return jsonify({"success": True, "message": "变体规则已删除。"})
    finally: conn.close()

# --- 其他 API ---
@app.route('/api/parse_rule', methods=['POST'])
def parse_rule_from_string():
    data = request.get_json()
    parse_string = data.get('text', '')
    parts = parse_string.split(',')
    if len(parts) != 2: return jsonify({"success": False, "message": "格式错误，应为 '番号,cid'"})
    bangou, cid = parts[0].strip(), parts[1].strip()
    try:
        rule = avbase_scraper.derive_rule_from_cid(bangou, cid)
        return jsonify({"success": True, "rule": rule})
    except ValueError as e: return jsonify({"success": False, "message": str(e)})

# --- 应用启动 ---
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=10086, debug=True)
