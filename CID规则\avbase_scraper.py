# avbase_scraper.py
import requests
from bs4 import BeautifulSoup
import urllib.parse
import re

HTTP_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
    'Referer': 'https://www.avbase.net/'
}
BANGOU_REGEX = re.compile(r'([a-zA-Z0-9\-_]+?)-?(\d+)')

class ScrapeError(Exception):
    """用于刮削失败的自定义异常"""
    pass

def scrape_cid(bangou: str) -> str:
    """
    从 avbase.net 搜索并解析出 CID
    """
    search_url = f"https://www.avbase.net/works?q={urllib.parse.quote(bangou)}"
    print(f"正在访问: {search_url}")
    try:
        response = requests.get(search_url, headers=HTTP_HEADERS, timeout=20)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'lxml')
        
        fanza_img = soup.find('img', alt='fanza')
        
        if not fanza_img:
            raise ScrapeError(f"在AVBase页面中未找到 'fanza' 图标 (可能无此番号记录或页面结构已更改)")
            
        fanza_anchor = fanza_img.find_parent('a')
        if not fanza_anchor or not fanza_anchor.has_attr('href'):
            raise ScrapeError("找到了'fanza'图标，但未能找到其包含链接的父标签")
            
        dmm_url_encoded = fanza_anchor['href']
        dmm_url_decoded = urllib.parse.unquote(dmm_url_encoded)
        
        match = re.search(r'cid=([a-zA-Z0-9_]+)', dmm_url_decoded)
        if not match:
            raise ScrapeError(f"在解码后的链接中未能解析出CID: {dmm_url_decoded}")
            
        found_cid = match.group(1)
        print(f"成功找到CID: {found_cid}")
        return found_cid
        
    except requests.exceptions.RequestException as e:
        raise ScrapeError(f"网络请求失败: {e}")

def derive_rule_from_cid(bangou: str, correct_cid: str) -> dict:
    """从番号和其对应的CID中推导出转换规则（前缀、补位、后缀等）"""
    match_bangou = BANGOU_REGEX.match(bangou.upper())
    if not match_bangou:
        raise ValueError(f"无法从番号 '{bangou}' 中解析出系列和数字。")
        
    series_name_upper = match_bangou.group(1)
    
    # Handle dummy cid case for modal lookup
    if correct_cid.lower() == 'dummycid':
        return {
            "series_name": series_name_upper,
            "prefix": "",
            "series_name_in_cid": series_name_upper.lower().replace('-', '').replace('_', ''),
            "padding": 0,
            "suffix": ""
        }

    series_name_lower = series_name_upper.lower()
    correct_cid_lower = correct_cid.lower()
    
    series_name_for_splitting = series_name_lower
    if series_name_lower not in correct_cid_lower:
        series_name_lower_no_hyphen = series_name_lower.replace('-', '').replace('_', '')
        if series_name_lower_no_hyphen in correct_cid_lower and series_name_lower_no_hyphen:
             series_name_for_splitting = series_name_lower_no_hyphen
        else:
            raise ValueError(f"抓取到的CID '{correct_cid}' 中不包含系列名 '{series_name_lower}' 或其变体 '{series_name_lower_no_hyphen}'")

    parts = correct_cid_lower.split(series_name_for_splitting)
    new_prefix = parts[0]
    numeric_part_and_suffix = parts[1]
    
    num_match = re.match(r'(\d+)(.*)', numeric_part_and_suffix)
    if not num_match:
        raise ValueError(f"无法从CID的后半部分 '{numeric_part_and_suffix}' 中解析出数字")
        
    new_padding = len(num_match.group(1))
    new_suffix = num_match.group(2)
    
    return {
        "series_name": series_name_upper,
        "prefix": new_prefix,
        "series_name_in_cid": series_name_for_splitting,
        "padding": new_padding,
        "suffix": new_suffix
    }
