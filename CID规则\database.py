# database.py
import sqlite3
import os

DB_FILE = "number_cid.db"

def column_exists(cursor, table_name, column_name):
    """检查表中是否存在指定的列"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def initialize_db():
    """初始化或更新数据库和表"""
    db_exists = os.path.exists(DB_FILE)
    conn = sqlite3.connect(DB_FILE)
    conn.execute("PRAGMA foreign_keys = ON;")
    cursor = conn.cursor()

    print(f"数据库 '{DB_FILE}' 检查开始...")

    # --- 1. 创建或验证 cid_conversion_rules (主规则表) ---
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cid_conversion_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_name TEXT NOT NULL UNIQUE,
        cid_prefix TEXT,
        series_name_in_cid TEXT,
        number_padding INTEGER NOT NULL,
        suffix TEXT,
        status TEXT DEFAULT '已确认'
    );
    ''')
    print("已确保 'cid_conversion_rules' (主规则) 表存在。")

    # --- MODIFICATION START: Add indexes for sorting and filtering performance ---
    print("正在检查并创建索引...")
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_rules_series_name ON cid_conversion_rules(series_name);')
    print("已为 'cid_conversion_rules' 表确保 'series_name' 列的索引存在。")
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_rules_number_padding ON cid_conversion_rules(number_padding);')
    print("已为 'cid_conversion_rules' 表确保 'number_padding' 列的索引存在。")
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_rules_status ON cid_conversion_rules(status);')
    print("已为 'cid_conversion_rules' 表确保 'status' 列的索引存在。")
    # --- MODIFICATION END ---

    # --- 2. 创建或验证 series_name_variants (变体规则表) ---
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS series_name_variants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        main_rule_id INTEGER NOT NULL,
        is_range_variant INTEGER NOT NULL DEFAULT 1, -- 1 for range, 0 for studio
        serial_number_range TEXT, 
        studio TEXT,
        cid_prefix TEXT,
        series_name_in_cid TEXT,
        number_padding INTEGER NOT NULL,
        suffix TEXT,
        description TEXT,
        FOREIGN KEY (main_rule_id) REFERENCES cid_conversion_rules (id) ON DELETE CASCADE
    );
    ''')
    # Add columns if they don't exist for backward compatibility
    if not column_exists(cursor, 'series_name_variants', 'is_range_variant'):
        cursor.execute('ALTER TABLE series_name_variants ADD COLUMN is_range_variant INTEGER NOT NULL DEFAULT 1;')
        print("已为 'series_name_variants' 表添加 'is_range_variant' 列。")
    if not column_exists(cursor, 'series_name_variants', 'studio'):
        cursor.execute('ALTER TABLE series_name_variants ADD COLUMN studio TEXT;')
        print("已为 'series_name_variants' 表添加 'studio' 列。")

    print("已确保 'series_name_variants' (变体规则) 表存在并更新了 schema。")

    if not db_exists:
        print("检测到全新数据库，正在插入示例数据...")
        try:
            cursor.execute("INSERT INTO cid_conversion_rules (series_name, cid_prefix, series_name_in_cid, number_padding, suffix) VALUES ('AGEMIX', 'h_213', 'agemix', 4, '')")
            main_rule_id = cursor.lastrowid
            cursor.execute("""
                INSERT INTO series_name_variants 
                (main_rule_id, is_range_variant, serial_number_range, cid_prefix, series_name_in_cid, number_padding, suffix, description) 
                VALUES (?, 1, '>=424', '', 'agemix', 4, '', '新版无 h_213 前缀')
            """, (main_rule_id,))
            print("已插入示例数据。")
        except sqlite3.Error as e:
            print(f"插入示例数据时出错: {e}")

    conn.commit()
    conn.close()
    print("数据库检查和初始化完成。")

if __name__ == "__main__":
    initialize_db()
