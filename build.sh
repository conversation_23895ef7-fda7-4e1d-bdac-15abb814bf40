#!/bin/bash

# JAssistant Docker 构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
IMAGE_NAME="jassistant"
IMAGE_TAG="latest"
DOCKERFILE="Dockerfile"

# 显示帮助信息
show_help() {
    echo "JAssistant Docker 构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -n, --name NAME     设置镜像名称 (默认: jassistant)"
    echo "  -t, --tag TAG       设置镜像标签 (默认: latest)"
    echo "  -f, --file FILE     指定 Dockerfile (默认: Dockerfile)"
    echo "  -a, --alpine        使用 Alpine 版本 (Dockerfile.alpine)"
    echo "  -c, --clean         构建前清理旧镜像"
    echo "  -p, --push          构建后推送到仓库"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                          # 使用默认设置构建"
    echo "  $0 -n myapp -t v1.0.0       # 构建 myapp:v1.0.0"
    echo "  $0 -a -c                    # 使用 Alpine 版本并清理旧镜像"
    echo "  $0 -t v2.0.0 -p             # 构建并推送 v2.0.0 版本"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE="$2"
            shift 2
            ;;
        -a|--alpine)
            DOCKERFILE="Dockerfile.alpine"
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}错误: 未知选项 $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 完整镜像名称
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}JAssistant Docker 构建脚本${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${YELLOW}镜像名称:${NC} ${FULL_IMAGE_NAME}"
echo -e "${YELLOW}Dockerfile:${NC} ${DOCKERFILE}"
echo ""

# 检查 Dockerfile 是否存在
if [[ ! -f "$DOCKERFILE" ]]; then
    echo -e "${RED}错误: Dockerfile '$DOCKERFILE' 不存在${NC}"
    exit 1
fi

# 清理旧镜像
if [[ "$CLEAN" == "true" ]]; then
    echo -e "${YELLOW}清理旧镜像...${NC}"
    
    # 删除同名镜像
    if docker images -q "$FULL_IMAGE_NAME" 2>/dev/null | grep -q .; then
        echo "删除镜像: $FULL_IMAGE_NAME"
        docker rmi "$FULL_IMAGE_NAME" || true
    fi
    
    # 清理悬空镜像
    if docker images -f "dangling=true" -q | grep -q .; then
        echo "清理悬空镜像..."
        docker image prune -f
    fi
    
    echo -e "${GREEN}清理完成${NC}"
    echo ""
fi

# 构建镜像
echo -e "${YELLOW}开始构建镜像...${NC}"
echo "docker build -f $DOCKERFILE -t $FULL_IMAGE_NAME ."
echo ""

if docker build -f "$DOCKERFILE" -t "$FULL_IMAGE_NAME" .; then
    echo ""
    echo -e "${GREEN}✅ 镜像构建成功: ${FULL_IMAGE_NAME}${NC}"
else
    echo ""
    echo -e "${RED}❌ 镜像构建失败${NC}"
    exit 1
fi

# 显示镜像信息
echo ""
echo -e "${YELLOW}镜像信息:${NC}"
docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"

# 推送镜像
if [[ "$PUSH" == "true" ]]; then
    echo ""
    echo -e "${YELLOW}推送镜像到仓库...${NC}"
    
    if docker push "$FULL_IMAGE_NAME"; then
        echo -e "${GREEN}✅ 镜像推送成功${NC}"
    else
        echo -e "${RED}❌ 镜像推送失败${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}构建完成！${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""
echo -e "${YELLOW}使用方法:${NC}"
echo "  # 运行容器"
echo "  docker run -d --name jassistant -p 34711:34711 $FULL_IMAGE_NAME"
echo ""
echo "  # 使用 Docker Compose"
echo "  docker-compose up -d"
echo ""
echo -e "${YELLOW}注意事项:${NC}"
echo "  1. 首次运行前请执行数据库迁移"
echo "  2. 确保正确配置 docker-compose.yml 中的路径映射"
echo "  3. 根据需要修改环境变量配置"
