import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { CheckCircleIcon, PaperAirplaneIcon, InformationCircleIcon, ArrowPathIcon } from '@heroicons/react/24/solid';

const WATERMARK_TARGETS = [ { id: 'poster', name: '封面 (Poster)' }, { id: 'thumb', name: '缩略图 (Thumb)' }, { id: 'fanart', name: '背景图 (Fanart)' }, ];

function SettingsPage() {
  const [settings, setSettings] = useState({});
  const [restartRequiredSettings, setRestartRequiredSettings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [restartNeeded, setRestartNeeded] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testStatus, setTestStatus] = useState(null); // null, 'success', 'error'
  const [testMessage, setTestMessage] = useState('');
  const [activeTab, setActiveTab] = useState('instant'); // 'instant', 'restart', 'performance'
  const [performancePresets, setPerformancePresets] = useState({});
  const [isApplyingPreset, setIsApplyingPreset] = useState(false);
  const [memoryStats, setMemoryStats] = useState({});
  const [isCleaningMemory, setIsCleaningMemory] = useState(false);
  const [configFileStatus, setConfigFileStatus] = useState({});

  useEffect(() => {
    // 加载设置
    axios.get('/api/settings')
      .then(res => {
        // 新的API响应格式包含settings和restart_required_settings
        setSettings(res.data.settings || res.data); // 兼容旧版API
        setRestartRequiredSettings(res.data.restart_required_settings || []);
      })
      .catch(err => console.error("加载设置失败", err))
      .finally(() => setLoading(false));

    // 加载性能预设
    axios.get('/api/performance-presets')
      .then(res => {
        if (res.data.success) {
          setPerformancePresets(res.data.presets);
        }
      })
      .catch(err => console.error("加载性能预设失败", err));

    // 加载内存统计
    loadMemoryStats();

    // 加载配置文件状态
    loadConfigFileStatus();
  }, []);

  const loadMemoryStats = () => {
    axios.get('/api/memory/stats')
      .then(res => {
        if (res.data.success) {
          setMemoryStats(res.data.data);
        }
      })
      .catch(err => console.error("加载内存统计失败", err));
  };

  const cleanupMemory = () => {
    if (!window.confirm('确定要清理内存吗？这将清空缓存并强制垃圾回收。')) {
      return;
    }

    setIsCleaningMemory(true);
    axios.post('/api/memory/cleanup')
      .then(res => {
        if (res.data.success) {
          alert(`内存清理成功！${res.data.message}`);
          loadMemoryStats(); // 重新加载内存统计
        } else {
          alert(`内存清理失败: ${res.data.message}`);
        }
      })
      .catch(err => {
        alert(`内存清理失败: ${err.response?.data?.message || err.message}`);
      })
      .finally(() => {
        setIsCleaningMemory(false);
      });
  };

  const loadConfigFileStatus = () => {
    axios.get('/api/config-file-status')
      .then(res => {
        if (res.data.success) {
          setConfigFileStatus(res.data.data);
        }
      })
      .catch(err => console.error("加载配置文件状态失败", err));
  };



  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };
  
  const handleWatermarkTargetChange = (e) => {
    const { name, checked } = e.target;
    setSettings(prev => {
      const currentTargets = prev.watermark_targets || [];
      if (checked) return { ...prev, watermark_targets: [...currentTargets, name] };
      return { ...prev, watermark_targets: currentTargets.filter(t => t !== name) };
    });
  };

  const handleSave = () => {
    // 获取当前完整设置，然后只更新当前标签页的设置
    let settingsToSave = { ...settings }; // 保持所有现有设置

    // 根据当前标签页确定要更新的设置键
    let keysToUpdate = [];

    if (activeTab === 'instant') {
      // 即时生效设置
      keysToUpdate = [
        'latest_movies_count', 'cover_size', 'homepage_aspect_ratio', 'secure_mode',
        'watermark_targets', 'watermark_scale_ratio', 'watermark_horizontal_offset',
        'watermark_vertical_offset', 'watermark_spacing', 'poster_crop_ratio',
        'notification_type', 'telegram_bot_token', 'telegram_chat_id',
        'telegram_random_image_api', 'notification_api_url', 'notification_route_id',
        'high_quality_min_height', 'high_quality_min_width', 'high_quality_min_size_kb'
      ];
    } else if (activeTab === 'restart') {
      // 需重启设置
      keysToUpdate = [
        'media_root', 'log_level', 'notification_enabled', 'notification_time'
      ];
    } else if (activeTab === 'performance') {
      // 性能参数设置
      keysToUpdate = [
        'performance_mode', 'cache_memory_size', 'cache_file_ttl', 'cache_image_ttl',
        'batch_size', 'max_concurrent_tasks', 'db_pool_size', 'worker_threads',
        'enable_personal_mode', 'enable_monitoring', 'monitor_cpu_threshold',
        'monitor_memory_threshold', 'monitor_disk_threshold',
        'memory_threshold_mb', 'memory_cleanup_interval', 'enable_auto_memory_cleanup'
      ];
    }

    // 只更新当前标签页相关的设置，保持其他设置不变
    keysToUpdate.forEach(key => {
      if (settings[key] !== undefined) {
        settingsToSave[key] = settings[key];
      }
    });

    // 确保版本信息存在
    if (settings.version) {
      settingsToSave.version = settings.version;
    }

    axios.post('/api/settings', settingsToSave)
      .then(response => {
          setShowSuccess(true);
          setTimeout(() => setShowSuccess(false), 2000);

          // 检查响应中的重启需求
          const needsRestart = response.data.restart_needed;
          setRestartNeeded(needsRestart);

          if (needsRestart) {
            // 如果需要重启，显示更详细的提示
            alert(`${activeTab === 'instant' ? '即时生效' : activeTab === 'restart' ? '需重启' : '性能参数'}设置已保存。您修改了需要重启容器才能生效的设置，请在方便的时候重启容器以应用这些更改。`);
          } else {
            alert(`${activeTab === 'instant' ? '即时生效' : activeTab === 'restart' ? '需重启' : '性能参数'}设置已保存并生效。`);
          }
      })
      .catch(err => alert("保存失败: " + err.response?.data?.message));
  };

  // 检查设置项是否需要重启生效
  const isRestartRequired = (settingKey) => {
    return restartRequiredSettings.includes(settingKey);
  };

  // 为需要重启的设置项添加重启图标
  const renderRestartIcon = (settingKey) => {
    if (isRestartRequired(settingKey)) {
      return (
        <div className="ml-2 inline-flex items-center text-amber-500" title="此设置需要重启容器才能生效">
          <ArrowPathIcon className="h-4 w-4" />
        </div>
      );
    }
    return null;
  };

  const handleTestNotification = () => {
    setIsTesting(true);
    setTestStatus(null);
    setTestMessage('');
    
    axios.post('/api/test-notification')
      .then(res => {
        setTestStatus('success');
        setTestMessage(res.data.message);
      })
      .catch(err => {
        setTestStatus('error');
        // 提取详细的错误信息
        let errorMsg = '测试失败';
        if (err.response && err.response.data && err.response.data.message) {
          errorMsg = err.response.data.message;
        } else if (err.message) {
          errorMsg = err.message;
        }
        setTestMessage(errorMsg);
      })
      .finally(() => setIsTesting(false));
  };

  const handleTestDailyReport = () => {
    setIsTesting(true);
    setTestStatus(null);
    setTestMessage('');

    axios.post('/api/test-daily-report')
      .then(res => {
        setTestStatus('success');
        setTestMessage(res.data.message);
      })
      .catch(err => {
        setTestStatus('error');
        let errorMsg = '测试失败';
        if (err.response && err.response.data && err.response.data.message) {
          errorMsg = err.response.data.message;
        } else if (err.message) {
          errorMsg = err.message;
        }
        setTestMessage(errorMsg);
      })
      .finally(() => setIsTesting(false));
  };



  const updateLogLevel = (level) => {
    axios.post('/api/update-log-level', { log_level: level })
      .then(res => {
        alert(res.data.message);
        // 更新本地设置
        setSettings(prev => ({ ...prev, log_level: level }));
      })
      .catch(err => alert(`更新日志级别失败: ${err.response?.data?.message || err.message}`));
  };
  
  const handleRestartContainer = () => {
    if (window.confirm('确定要重启容器吗？这将短暂中断服务。')) {
      axios.post('/api/restart-container')
        .then(res => {
          alert('容器正在重启，请稍后刷新页面。');
        })
        .catch(err => {
          alert(`重启容器失败: ${err.response?.data?.message || err.message}`);
        });
    }
  };

  const applyPerformancePreset = (mode) => {
    if (!window.confirm(`确定要应用${mode}性能模式吗？这将覆盖当前的性能参数设置。`)) {
      return;
    }

    setIsApplyingPreset(true);
    axios.post('/api/apply-performance-preset', { mode })
      .then(res => {
        if (res.data.success) {
          setSettings(res.data.settings);
          alert(`已成功应用${mode}性能模式配置！${res.data.restart_needed ? '需要重启容器才能完全生效。' : ''}`);
          if (res.data.restart_needed) {
            setRestartNeeded(true);
          }
        } else {
          alert(`应用性能预设失败: ${res.data.message}`);
        }
      })
      .catch(err => {
        alert(`应用性能预设失败: ${err.response?.data?.message || err.message}`);
      })
      .finally(() => {
        setIsApplyingPreset(false);
      });
  };

  if (loading) return <p className="text-[var(--color-secondary-text)]">加载设置中...</p>;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-4xl font-bold text-[var(--color-primary-text)]">系统设置</h1>
        <div className="flex items-center gap-4">
          <button 
            onClick={handleRestartContainer}
            className="flex items-center gap-2 bg-amber-500 text-white px-3 py-1 rounded-md hover:bg-amber-600 transition-colors"
          >
            <ArrowPathIcon className="h-4 w-4" />
            重启容器
          </button>
          <div className="flex items-center gap-1 text-sm text-[var(--color-secondary-text)]">
            <InformationCircleIcon className="h-5 w-5" />
            <span>版本: {settings.version || 'v0.0.1'}</span>
          </div>
        </div>
      </div>
      
      {restartNeeded && (
        <div className="mb-6 p-4 bg-amber-100 border-l-4 border-amber-500 text-amber-700 rounded">
          <div className="flex items-center">
            <ArrowPathIcon className="h-6 w-6 mr-2" />
            <div>
              <p className="font-bold">需要重启容器</p>
              <p>您修改了需要重启才能生效的设置项。请点击右上角的"重启容器"按钮应用这些更改。</p>
            </div>
          </div>
        </div>
      )}
      
      {/* 设置分类标签 */}
      <div className="flex mb-6 border-b border-[var(--color-border)]">
        <button 
          onClick={() => setActiveTab('instant')}
          className={`py-2 px-4 font-medium text-lg ${activeTab === 'instant' 
            ? 'text-[var(--color-primary-accent)] border-b-2 border-[var(--color-primary-accent)]' 
            : 'text-[var(--color-secondary-text)] hover:text-[var(--color-primary-text)]'}`}
        >
          即时生效设置
        </button>
        <button
          onClick={() => setActiveTab('restart')}
          className={`py-2 px-4 font-medium text-lg flex items-center ${activeTab === 'restart'
            ? 'text-[var(--color-primary-accent)] border-b-2 border-[var(--color-primary-accent)]'
            : 'text-[var(--color-secondary-text)] hover:text-[var(--color-primary-text)]'}`}
        >
          需重启设置
          {restartNeeded && <span className="ml-2 bg-amber-500 text-white text-xs px-2 py-1 rounded-full">已修改</span>}
        </button>
        <button
          onClick={() => setActiveTab('performance')}
          className={`py-2 px-4 font-medium text-lg ${activeTab === 'performance'
            ? 'text-[var(--color-primary-accent)] border-b-2 border-[var(--color-primary-accent)]'
            : 'text-[var(--color-secondary-text)] hover:text-[var(--color-primary-text)]'}`}
        >
          性能参数
        </button>
      </div>
      
      <div className="bg-[var(--color-secondary-bg)] p-6 rounded-lg max-w-2xl space-y-8">
        {activeTab === 'instant' && (
          <>
            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">主页显示</legend>
              <div>
                <label htmlFor="latest_movies_count" className="block text-lg font-medium text-[var(--color-primary-text)]">最新入库显示数量</label>
                <input type="number" name="latest_movies_count" id="latest_movies_count" value={settings.latest_movies_count || 24} onChange={handleChange} className="input-field mt-1" />
              </div>
              <div>
                <label htmlFor="cover_size" className="block text-lg font-medium text-[var(--color-primary-text)]">封面显示大小</label>
                <select name="cover_size" id="cover_size" value={settings.cover_size || 'medium'} onChange={handleChange} className="input-field mt-1">
                  <option value="small">小</option>
                  <option value="medium">中</option>
                  <option value="large">大</option>
                </select>
              </div>
              <div>
                <label htmlFor="homepage_aspect_ratio" className="block text-lg font-medium text-[var(--color-primary-text)]">主页封面显示比例</label>
                <select name="homepage_aspect_ratio" id="homepage_aspect_ratio" value={settings.homepage_aspect_ratio || '2:3'} onChange={handleChange} className="input-field mt-1">
                  <option value="2:3">2:3 (Emby 默认)</option>
                  <option value="2.12:3">2.12:3 (封面默认)</option>
                </select>
              </div>
              <div className="flex items-center">
                <input type="checkbox" name="secure_mode" id="secure_mode" checked={settings.secure_mode || false} onChange={handleChange} className="h-5 w-5 rounded bg-[var(--color-secondary-bg)] border-[var(--color-border)] text-[var(--color-primary-accent)] focus:ring-[var(--color-primary-accent)]" />
                <label htmlFor="secure_mode" className="ml-3 text-lg font-medium text-[var(--color-primary-text)]">安全模式 (模糊显示主页图片)</label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" name="use_cover_cache" id="use_cover_cache" checked={settings.use_cover_cache !== false} onChange={handleChange} className="h-5 w-5 rounded bg-[var(--color-secondary-bg)] border-[var(--color-border)] text-[var(--color-primary-accent)] focus:ring-[var(--color-primary-accent)]" />
                <label htmlFor="use_cover_cache" className="ml-3 text-lg font-medium text-[var(--color-primary-text)]">启用封面缓存 (减少读取媒体库)</label>
              </div>
              <div>
                <label htmlFor="cover_cache_dir" className="block text-lg font-medium text-[var(--color-primary-text)]">封面缓存目录</label>
                <input type="text" name="cover_cache_dir" id="cover_cache_dir" value={settings.cover_cache_dir || 'cover_cache'} onChange={handleChange} className="input-field mt-1" />
                <p className="text-sm text-[var(--color-secondary-text)] mt-1">相对于容器内部的路径，默认为 cover_cache</p>
              </div>
            </fieldset>
            
            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">水印处理</legend>
              <div>
                <h4 className="text-lg font-medium text-[var(--color-primary-text)] mb-2">应用目标</h4>
                {WATERMARK_TARGETS.map((target) => (
                    <div key={target.id} className="flex items-center"><input id={target.id} name={target.id} type="checkbox" checked={settings.watermark_targets?.includes(target.id)} onChange={handleWatermarkTargetChange} className="h-5 w-5 rounded bg-[var(--color-secondary-bg)] border-[var(--color-border)] text-[var(--color-primary-accent)] focus:ring-[var(--color-primary-accent)]" /><label htmlFor={target.id} className="ml-3 text-lg text-[var(--color-primary-text)]">{target.name}</label></div>
                ))}
              </div>
              <div>
                <label htmlFor="watermark_scale_ratio" className="block text-lg font-medium text-[var(--color-primary-text)]">缩放倍率 (图片高/水印高)</label>
                <input type="number" name="watermark_scale_ratio" value={settings.watermark_scale_ratio || 12} onChange={handleChange} className="input-field mt-1" />
              </div>

              {/* 海报水印位置设置 */}
              <div className="mt-6">
                <h4 className="text-lg font-semibold text-[var(--color-primary-text)] mb-2">海报水印位置比例</h4>
                <p className="text-sm text-[var(--color-secondary-text)] mb-4">基于1032×1468尺寸，35px/10px/10px换算的比例</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="watermark_poster_horizontal_ratio" className="block text-sm font-medium text-[var(--color-primary-text)]">横向边距比例 (基于宽度)</label>
                    <input type="number" step="0.0001" name="watermark_poster_horizontal_ratio" value={settings.watermark_poster_horizontal_ratio || 0.0339} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-xs text-[var(--color-secondary-text)] mt-1">3.39% = 35px ÷ 1032px (宽度)</p>
                    <p className="text-xs text-[var(--color-secondary-text)]">计算公式: 图片宽度 × 比例</p>
                  </div>
                  <div>
                    <label htmlFor="watermark_poster_vertical_ratio" className="block text-sm font-medium text-[var(--color-primary-text)]">纵向边距比例 (基于高度)</label>
                    <input type="number" step="0.0001" name="watermark_poster_vertical_ratio" value={settings.watermark_poster_vertical_ratio || 0.0068} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-xs text-[var(--color-secondary-text)] mt-1">0.68% = 10px ÷ 1468px (高度)</p>
                    <p className="text-xs text-[var(--color-secondary-text)]">计算公式: 图片高度 × 比例</p>
                  </div>
                  <div>
                    <label htmlFor="watermark_poster_spacing_ratio" className="block text-sm font-medium text-[var(--color-primary-text)]">水印间距比例 (基于宽度)</label>
                    <input type="number" step="0.0001" name="watermark_poster_spacing_ratio" value={settings.watermark_poster_spacing_ratio || 0.0097} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-xs text-[var(--color-secondary-text)] mt-1">0.97% = 10px ÷ 1032px (宽度)</p>
                    <p className="text-xs text-[var(--color-secondary-text)]">计算公式: 图片宽度 × 比例</p>
                  </div>
                </div>
              </div>

              {/* 缩略图水印位置设置 */}
              <div className="mt-6">
                <h4 className="text-lg font-semibold text-[var(--color-primary-text)] mb-2">缩略图水印位置比例</h4>
                <p className="text-sm text-[var(--color-secondary-text)] mb-4">基于2184×1468尺寸，35px/10px/10px换算的比例</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="watermark_thumb_horizontal_ratio" className="block text-sm font-medium text-[var(--color-primary-text)]">横向边距比例 (基于宽度)</label>
                    <input type="number" step="0.0001" name="watermark_thumb_horizontal_ratio" value={settings.watermark_thumb_horizontal_ratio || 0.0160} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-xs text-[var(--color-secondary-text)] mt-1">1.60% = 35px ÷ 2184px (宽度)</p>
                    <p className="text-xs text-[var(--color-secondary-text)]">计算公式: 图片宽度 × 比例</p>
                  </div>
                  <div>
                    <label htmlFor="watermark_thumb_vertical_ratio" className="block text-sm font-medium text-[var(--color-primary-text)]">纵向边距比例 (基于高度)</label>
                    <input type="number" step="0.0001" name="watermark_thumb_vertical_ratio" value={settings.watermark_thumb_vertical_ratio || 0.0068} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-xs text-[var(--color-secondary-text)] mt-1">0.68% = 10px ÷ 1468px (高度)</p>
                    <p className="text-xs text-[var(--color-secondary-text)]">计算公式: 图片高度 × 比例</p>
                  </div>
                  <div>
                    <label htmlFor="watermark_thumb_spacing_ratio" className="block text-sm font-medium text-[var(--color-primary-text)]">水印间距比例 (基于宽度)</label>
                    <input type="number" step="0.0001" name="watermark_thumb_spacing_ratio" value={settings.watermark_thumb_spacing_ratio || 0.0046} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-xs text-[var(--color-secondary-text)] mt-1">0.46% = 10px ÷ 2184px (宽度)</p>
                    <p className="text-xs text-[var(--color-secondary-text)]">计算公式: 图片宽度 × 比例</p>
                  </div>
                </div>
              </div>
            </fieldset>
            
            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">图片裁剪与质量判断</legend>
              <div>
                <label htmlFor="poster_crop_ratio" className="block text-lg font-medium text-[var(--color-primary-text)]">海报裁剪比例 (高/宽)</label>
                <p className="text-sm text-[var(--color-secondary-text)] mb-1">默认值为 1.415</p>
                <input type="number" step="0.001" name="poster_crop_ratio" value={settings.poster_crop_ratio || 1.415} onChange={handleChange} className="input-field mt-1" />
              </div>
              
              <div>
                <label htmlFor="high_quality_min_height" className="block text-lg font-medium text-[var(--color-primary-text)]">高画质最小高度 (像素)</label>
                <input type="number" name="high_quality_min_height" value={settings.high_quality_min_height || 800} onChange={handleChange} className="input-field mt-1" />
              </div>
              
              <div>
                <label htmlFor="high_quality_min_width" className="block text-lg font-medium text-[var(--color-primary-text)]">高画质最小宽度 (像素)</label>
                <input type="number" name="high_quality_min_width" value={settings.high_quality_min_width || 450} onChange={handleChange} className="input-field mt-1" />
              </div>
              
              <div>
                <label htmlFor="high_quality_min_size_kb" className="block text-lg font-medium text-[var(--color-primary-text)]">高画质最小文件大小 (KB)</label>
                <input type="number" name="high_quality_min_size_kb" value={settings.high_quality_min_size_kb || 50} onChange={handleChange} className="input-field mt-1" />
              </div>
            </fieldset>
            
            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">通知设置</legend>
              <div>
                <label htmlFor="notification_type" className="block text-lg font-medium text-[var(--color-primary-text)]">通知方式</label>
                <select name="notification_type" id="notification_type" value={settings.notification_type || 'custom'} onChange={handleChange} className="input-field mt-1">
                  <option value="custom">自定义通知</option>
                  <option value="telegram">Telegram机器人</option>
                </select>
              </div>
              
              {settings.notification_type === 'custom' && (
                <>
                  <div>
                    <label htmlFor="notification_api_url" className="block text-lg font-medium text-[var(--color-primary-text)]">接口地址</label>
                    <input type="text" name="notification_api_url" value={settings.notification_api_url || ''} onChange={handleChange} className="input-field mt-1" />
                  </div>
                  <div>
                    <label htmlFor="notification_route_id" className="block text-lg font-medium text-[var(--color-primary-text)]">Route ID</label>
                    <input type="text" name="notification_route_id" value={settings.notification_route_id || ''} onChange={handleChange} className="input-field mt-1" />
                  </div>
                </>
              )}
              
              {settings.notification_type === 'telegram' && (
                <>
                  <div>
                    <label htmlFor="telegram_bot_token" className="block text-lg font-medium text-[var(--color-primary-text)]">Bot Token</label>
                    <input type="text" name="telegram_bot_token" value={settings.telegram_bot_token || ''} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-sm text-[var(--color-secondary-text)] mt-1">从 BotFather 获取的机器人Token</p>
                  </div>
                  <div>
                    <label htmlFor="telegram_chat_id" className="block text-lg font-medium text-[var(--color-primary-text)]">Chat ID</label>
                    <input type="text" name="telegram_chat_id" value={settings.telegram_chat_id || ''} onChange={handleChange} className="input-field mt-1" />
                    <p className="text-sm text-[var(--color-secondary-text)] mt-1">接收通知的用户ID或群组ID</p>
                  </div>
                  
                  <div className="mt-4">
                    <label htmlFor="telegram_random_image_api" className="block text-lg font-medium text-[var(--color-primary-text)]">随机图片API</label>
                    <input type="text" name="telegram_random_image_api" value={settings.telegram_random_image_api || ''} onChange={handleChange} className="input-field mt-1" placeholder="输入随机图片API地址" />
                    <p className="text-sm text-[var(--color-secondary-text)] mt-1">随机图片API的URL，留空则不发送图片</p>
                  </div>
                </>
              )}
              
              <div>
                <div className="flex gap-2">
                  <button
                    onClick={handleTestNotification}
                    disabled={isTesting}
                    className="flex items-center justify-center gap-2 bg-[var(--color-sidebar-bg)] text-[var(--color-primary-text)] px-4 py-2 rounded-md text-sm font-semibold hover:bg-opacity-80 transition-colors disabled:opacity-50">
                    <PaperAirplaneIcon className="h-5 w-5"/>
                    {isTesting ? '发送中...' : '发送测试通知'}
                  </button>
                  <button
                    onClick={handleTestDailyReport}
                    disabled={isTesting}
                    className="flex items-center justify-center gap-2 bg-[var(--color-secondary-accent)] text-white px-4 py-2 rounded-md text-sm font-semibold hover:bg-opacity-80 transition-colors disabled:opacity-50">
                    <PaperAirplaneIcon className="h-5 w-5"/>
                    {isTesting ? '测试中...' : '测试每日报告'}
                  </button>
                </div>
                {testStatus === 'success' && (
                  <div className="mt-2 text-sm text-green-500">
                    {testMessage || '通知发送成功，请检查您的通知服务。'}
                  </div>
                )}
                {testStatus === 'error' && (
                  <div className="mt-2 text-sm text-red-500">
                    <p><strong>错误：</strong> {testMessage}</p>
                    <p className="mt-1">可能的解决方法:</p>
                    <ul className="list-disc pl-5 mt-1">
                      <li>检查接口地址或Token是否正确</li>
                      <li>确认目标服务器是否可访问</li>
                      <li>尝试增加超时时间</li>
                      <li>查看系统日志获取更多信息</li>
                    </ul>
                  </div>
                )}
              </div>
            </fieldset>
          </>
        )}
        
        {activeTab === 'restart' && (
          <>
            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">
                系统设置
                <span className="text-amber-500 ml-2 text-sm font-normal">需要重启容器才能生效</span>
              </legend>
              
              <div>
                <label htmlFor="media_root" className="block text-lg font-medium text-[var(--color-primary-text)] flex items-center">
                  媒体根路径
                  <ArrowPathIcon className="h-4 w-4 ml-2 text-amber-500" />
                </label>
                <input type="text" name="media_root" id="media_root" value={settings.media_root || '/weiam'} onChange={handleChange} className="input-field mt-1" />
                <p className="text-sm text-[var(--color-secondary-text)] mt-1">必须与Docker容器内的挂载路径一致</p>
              </div>
              
              <div>
                <label htmlFor="log_level" className="block text-lg font-medium text-[var(--color-primary-text)] flex items-center">
                  日志级别
                  <ArrowPathIcon className="h-4 w-4 ml-2 text-amber-500" />
                </label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'].map(level => (
                    <button
                      key={level}
                      onClick={() => updateLogLevel(level)}
                      className={`px-4 py-2 rounded-md ${settings.log_level === level 
                        ? 'bg-[var(--color-primary-accent)] text-white font-bold' 
                        : 'bg-[var(--color-sidebar-bg)] text-[var(--color-primary-text)]'}`}
                    >
                      {level}
                    </button>
                  ))}
                </div>
                
                <p className="mt-2 text-sm text-[var(--color-secondary-text)]">
                  当前日志级别: <span className="font-bold">{settings.log_level || 'INFO'}</span>
                  <br/>
                  <span className="text-amber-500">注意：更改日志级别可立即生效，但需要重启容器才能完全应用于所有组件</span>
                </p>
              </div>
            </fieldset>
            
            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">
                通知计划
                <span className="text-amber-500 ml-2 text-sm font-normal">需要重启容器才能生效</span>
              </legend>
              
              <div className="flex items-center">
                <input type="checkbox" name="notification_enabled" id="notification_enabled" checked={settings.notification_enabled || false} onChange={handleChange} className="h-5 w-5 rounded bg-[var(--color-secondary-bg)] border-[var(--color-border)] text-[var(--color-primary-accent)] focus:ring-[var(--color-primary-accent)]" />
                <label htmlFor="notification_enabled" className="ml-3 text-lg font-medium text-[var(--color-primary-text)] flex items-center">
                  启用每日入库通知
                  <ArrowPathIcon className="h-4 w-4 ml-2 text-amber-500" />
                </label>
              </div>
              
              <div>
                <label htmlFor="notification_time" className="block text-lg font-medium text-[var(--color-primary-text)] flex items-center">
                  通知时间
                  <ArrowPathIcon className="h-4 w-4 ml-2 text-amber-500" />
                </label>
                <input type="time" name="notification_time" value={settings.notification_time || '09:00'} onChange={handleChange} className="input-field mt-1" />
                <p className="text-sm text-amber-500 mt-1">修改通知时间后需要重启容器，才能按新时间发送通知</p>
              </div>
            </fieldset>
          </>
        )}

        {activeTab === 'performance' && (
          <>
            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">
                性能模式预设
                <span className="text-amber-500 ml-2 text-sm font-normal">推荐使用中配置</span>
              </legend>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {['low', 'medium', 'high'].map((mode) => {
                  const preset = performancePresets[mode];
                  if (!preset) return null;
                  return (
                    <div key={mode} className={`p-4 border rounded-lg ${settings.performance_mode === mode ? 'border-[var(--color-primary-accent)] bg-[var(--color-primary-accent)]/10' : 'border-[var(--color-border)]'}`}>
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-bold text-lg capitalize">{mode === 'low' ? '低配置' : mode === 'medium' ? '中配置' : '高配置'}</h3>
                        {settings.performance_mode === mode && (
                          <span className="text-xs bg-[var(--color-primary-accent)] text-white px-2 py-1 rounded">当前</span>
                        )}
                      </div>
                    <div className="text-sm text-[var(--color-secondary-text)] space-y-1">
                      <p>内存缓存: {preset.cache_memory_size}</p>
                      <p>文件缓存: {Math.floor(preset.cache_file_ttl/60)}分钟</p>
                      <p>批量大小: {preset.batch_size}</p>
                      <p>并发任务: {preset.max_concurrent_tasks}</p>
                      <p>工作线程: {preset.worker_threads}</p>
                    </div>
                    <button
                      onClick={() => applyPerformancePreset(mode)}
                      disabled={isApplyingPreset || settings.performance_mode === mode}
                      className={`mt-3 w-full py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                        settings.performance_mode === mode
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-[var(--color-primary-accent)] text-white hover:bg-opacity-80'
                      }`}
                    >
                      {isApplyingPreset ? '应用中...' : settings.performance_mode === mode ? '已应用' : '应用此配置'}
                    </button>
                  </div>
                  );
                })}
              </div>
            </fieldset>

            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">
                缓存设置
                <span className="text-amber-500 ml-2 text-sm font-normal">需要重启容器才能生效</span>
              </legend>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="cache_memory_size" className="block text-lg font-medium text-[var(--color-primary-text)]">内存缓存大小</label>
                  <input type="number" name="cache_memory_size" id="cache_memory_size" value={settings.cache_memory_size || 1000} onChange={handleChange} className="input-field mt-1" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">缓存项目数量，建议1000-5000</p>
                </div>

                <div>
                  <label htmlFor="cache_file_ttl" className="block text-lg font-medium text-[var(--color-primary-text)]">文件缓存时间(秒)</label>
                  <input type="number" name="cache_file_ttl" id="cache_file_ttl" value={settings.cache_file_ttl || 3600} onChange={handleChange} className="input-field mt-1" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">文件缓存保存时间，建议1800-14400</p>
                </div>

                <div>
                  <label htmlFor="cache_image_ttl" className="block text-lg font-medium text-[var(--color-primary-text)]">图片缓存时间(秒)</label>
                  <input type="number" name="cache_image_ttl" id="cache_image_ttl" value={settings.cache_image_ttl || 86400} onChange={handleChange} className="input-field mt-1" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">
                    远程URL图片缓存时间，建议43200-259200<br/>
                    <span className="text-green-600">✓ 缓存远程下载的图片（格式：原文件名_MD5.ext）</span><br/>
                    <span className="text-gray-500">✗ 本地上传的图片不缓存</span>
                  </p>
                </div>
              </div>
            </fieldset>

            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">
                批量处理设置
                <span className="text-amber-500 ml-2 text-sm font-normal">需要重启容器才能生效</span>
              </legend>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="batch_size" className="block text-lg font-medium text-[var(--color-primary-text)]">批量处理大小</label>
                  <input type="number" name="batch_size" id="batch_size" value={settings.batch_size || 100} onChange={handleChange} className="input-field mt-1" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">单次批量操作的项目数量</p>
                </div>

                <div>
                  <label htmlFor="max_concurrent_tasks" className="block text-lg font-medium text-[var(--color-primary-text)]">最大并发任务</label>
                  <input type="number" name="max_concurrent_tasks" id="max_concurrent_tasks" value={settings.max_concurrent_tasks || 5} onChange={handleChange} className="input-field mt-1" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">同时执行的任务数量</p>
                </div>

                <div>
                  <label htmlFor="worker_threads" className="block text-lg font-medium text-[var(--color-primary-text)]">工作线程数</label>
                  <input type="number" name="worker_threads" id="worker_threads" value={settings.worker_threads || 4} onChange={handleChange} className="input-field mt-1" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">处理任务的线程数量</p>
                </div>

                <div>
                  <label htmlFor="db_pool_size" className="block text-lg font-medium text-[var(--color-primary-text)]">数据库连接池大小</label>
                  <input type="number" name="db_pool_size" id="db_pool_size" value={settings.db_pool_size || 10} onChange={handleChange} className="input-field mt-1" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">数据库连接池的连接数量</p>
                </div>
              </div>
            </fieldset>

            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">
                内存管理
              </legend>

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-medium text-blue-800">当前内存使用情况</h4>
                  <button
                    onClick={loadMemoryStats}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    🔄 刷新
                  </button>
                </div>

                {memoryStats.current_memory && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-800">
                        {memoryStats.current_memory.rss_mb?.toFixed(1) || 0}MB
                      </div>
                      <div className="text-xs text-blue-600">物理内存</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-800">
                        {memoryStats.current_memory.percent?.toFixed(1) || 0}%
                      </div>
                      <div className="text-xs text-blue-600">内存占用</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-800">
                        {memoryStats.threshold_mb || 150}MB
                      </div>
                      <div className="text-xs text-blue-600">清理阈值</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-bold ${memoryStats.should_cleanup ? 'text-red-600' : 'text-green-600'}`}>
                        {memoryStats.should_cleanup ? '需要清理' : '正常'}
                      </div>
                      <div className="text-xs text-blue-600">状态</div>
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <button
                    onClick={cleanupMemory}
                    disabled={isCleaningMemory}
                    className={`px-4 py-2 rounded font-medium transition-colors ${
                      isCleaningMemory
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-red-500 text-white hover:bg-red-600'
                    }`}
                  >
                    {isCleaningMemory ? '清理中...' : '🗑️ 立即清理内存'}
                  </button>

                  <div className="text-sm text-blue-600 flex items-center">
                    <span>自动清理: {memoryStats.auto_cleanup_enabled ? '✅ 已启用' : '❌ 已禁用'}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="memory_threshold_mb" className="block text-lg font-medium text-[var(--color-primary-text)]">内存清理阈值(MB)</label>
                  <input type="number" name="memory_threshold_mb" id="memory_threshold_mb" value={settings.memory_threshold_mb || 150} onChange={handleChange} className="input-field mt-1" min="50" max="500" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">超过此值时自动清理内存</p>
                </div>

                <div>
                  <label htmlFor="memory_cleanup_interval" className="block text-lg font-medium text-[var(--color-primary-text)]">检查间隔(秒)</label>
                  <input type="number" name="memory_cleanup_interval" id="memory_cleanup_interval" value={settings.memory_cleanup_interval || 300} onChange={handleChange} className="input-field mt-1" min="60" max="3600" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">内存检查的时间间隔</p>
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="enable_auto_memory_cleanup"
                      checked={settings.enable_auto_memory_cleanup !== false}
                      onChange={handleChange}
                      className="mr-2 w-4 h-4"
                    />
                    <span className="text-lg font-medium text-[var(--color-primary-text)]">启用自动内存清理</span>
                  </label>
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">定期检查并自动清理内存，保持应用性能</p>
                </div>
              </div>
            </fieldset>

            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">
                监控告警设置
                <span className="text-amber-500 ml-2 text-sm font-normal">需要重启容器才能生效</span>
              </legend>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      name="enable_monitoring"
                      checked={settings.enable_monitoring !== false}
                      onChange={handleChange}
                      className="mr-2 w-4 h-4"
                    />
                    <span className="text-lg font-medium text-[var(--color-primary-text)]">启用监控告警</span>
                  </label>
                  <p className="text-sm text-[var(--color-secondary-text)]">开启系统资源监控和告警通知</p>
                </div>

                <div>
                  <label htmlFor="monitor_cpu_threshold" className="block text-lg font-medium text-[var(--color-primary-text)]">CPU告警阈值(%)</label>
                  <input type="number" name="monitor_cpu_threshold" id="monitor_cpu_threshold" value={settings.monitor_cpu_threshold || 90} onChange={handleChange} className="input-field mt-1" min="50" max="99" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">CPU使用率超过此值时告警</p>
                </div>

                <div>
                  <label htmlFor="monitor_memory_threshold" className="block text-lg font-medium text-[var(--color-primary-text)]">内存告警阈值(%)</label>
                  <input type="number" name="monitor_memory_threshold" id="monitor_memory_threshold" value={settings.monitor_memory_threshold || 90} onChange={handleChange} className="input-field mt-1" min="50" max="99" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">内存使用率超过此值时告警</p>
                </div>

                <div>
                  <label htmlFor="monitor_disk_threshold" className="block text-lg font-medium text-[var(--color-primary-text)]">磁盘告警阈值(%)</label>
                  <input type="number" name="monitor_disk_threshold" id="monitor_disk_threshold" value={settings.monitor_disk_threshold || 95} onChange={handleChange} className="input-field mt-1" min="70" max="99" />
                  <p className="text-sm text-[var(--color-secondary-text)] mt-1">磁盘使用率超过此值时告警</p>
                </div>
              </div>
            </fieldset>

            <fieldset className="space-y-4">
              <legend className="text-xl font-semibold text-[var(--color-primary-accent)] border-b border-[var(--color-border)] pb-2 mb-4">个人模式设置</legend>

              <div className="bg-[var(--color-primary-bg)] p-4 rounded-lg border border-[var(--color-border)]">
                <label className="flex items-center mb-3">
                  <input
                    type="checkbox"
                    name="enable_personal_mode"
                    checked={settings.enable_personal_mode || false}
                    onChange={handleChange}
                    className="mr-3 w-4 h-4"
                  />
                  <span className="text-lg font-medium text-[var(--color-primary-text)]">启用个人模式</span>
                </label>

                <div className="ml-7 space-y-2 text-sm">
                  <div className={`p-3 rounded-md ${settings.enable_personal_mode ? 'bg-green-100 border border-green-300' : 'bg-gray-100 border border-gray-300'}`}>
                    <p className="font-medium text-gray-800 mb-1">
                      {settings.enable_personal_mode ? '✅ 个人模式已启用' : '⚠️ 严格模式已启用'}
                    </p>
                    {settings.enable_personal_mode ? (
                      <div className="text-green-700">
                        <p>• 简化路径安全检查，只防护基本的路径遍历攻击</p>
                        <p>• 适合局域网环境下的个人使用</p>
                        <p>• 文件访问性能更高，响应更快</p>
                        <p>• 推荐用于1-2人的个人或家庭环境</p>
                      </div>
                    ) : (
                      <div className="text-gray-700">
                        <p>• 严格的路径安全检查和权限验证</p>
                        <p>• 适合多用户或公网环境</p>
                        <p>• 安全性更高，但性能相对较低</p>
                        <p>• 推荐用于多用户共享或对外开放的环境</p>
                      </div>
                    )}
                  </div>

                  <p className="text-[var(--color-secondary-text)] italic">
                    💡 提示：局域网个人使用建议启用个人模式以获得更好的性能体验
                  </p>
                </div>
              </div>
            </fieldset>
          </>
        )}
        
        {/* 配置文件状态信息 */}
        {configFileStatus.config_file_path && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-gray-800">配置文件状态</h4>
              <button
                onClick={loadConfigFileStatus}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                🔄 刷新
              </button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
              <div>
                <span className="text-gray-600">配置文件:</span>
                <div className={`font-mono ${configFileStatus.config_file_exists ? 'text-green-600' : 'text-red-600'}`}>
                  {configFileStatus.config_file_exists ? '✅ 存在' : '❌ 不存在'}
                </div>
              </div>
              <div>
                <span className="text-gray-600">目录:</span>
                <div className={`font-mono ${configFileStatus.config_dir_exists ? 'text-green-600' : 'text-red-600'}`}>
                  {configFileStatus.config_dir_exists ? '✅ 存在' : '❌ 不存在'}
                </div>
              </div>
              <div>
                <span className="text-gray-600">可写:</span>
                <div className={`font-mono ${configFileStatus.config_dir_writable ? 'text-green-600' : 'text-red-600'}`}>
                  {configFileStatus.config_dir_writable ? '✅ 是' : '❌ 否'}
                </div>
              </div>
              <div>
                <span className="text-gray-600">路径:</span>
                <div className="font-mono text-gray-700 truncate" title={configFileStatus.config_file_path}>
                  {configFileStatus.config_file_path}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="pt-4 border-t border-[var(--color-border)]">
          <div className="flex items-center justify-between">
            <div>
              <button onClick={handleSave} className="bg-[var(--color-primary-accent)] hover:bg-opacity-80 text-white font-bold py-2 px-4 rounded-md inline-flex items-center transition-colors">
                保存{activeTab === 'instant' ? '即时生效' : activeTab === 'restart' ? '需重启' : '性能参数'}设置
              </button>
              {showSuccess && <span className="inline-flex items-center gap-2 text-[var(--color-secondary-accent)] ml-4"><CheckCircleIcon className="h-6 w-6" /> 保存成功!</span>}
            </div>
            
            {restartNeeded && (
              <button 
                onClick={handleRestartContainer}
                className="bg-amber-500 hover:bg-amber-600 text-white font-bold py-2 px-4 rounded-md inline-flex items-center transition-colors"
              >
                <ArrowPathIcon className="h-5 w-5 mr-2" />
                应用更改并重启
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default SettingsPage;
